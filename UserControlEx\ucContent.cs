using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Language;
using OCRTools.Properties;
using OCRTools.WebBroswerEx;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class UcContent : UserControl
    {
        private SpiltMode _currentSpiltModel;

        private bool _isShowOldContent;

        private ContextMenuStrip _menuStrip;

        /// <summary>
        /// 图片缩放策略
        /// </summary>
        public ImageBox.ImageScalingStrategy ScalingStrategy
        {
            get { return imageBox.ScalingStrategy; }
            set { if (imageBox != null) imageBox.ScalingStrategy = value; }
        }

        private bool _isImageMode;
        public bool IsImageMode
        {
            get { return _isImageMode; }
            set
            {
                _isImageMode = value;
                if (value)
                {
                    imageBox.AutoCenter = true;
                    tsmPicType.Visible = true;
                    tsmEdit.Visible = true;
                    InitImageType();
                }
            }
        }

        /// <summary>
        /// 网页控件延迟加载属性
        /// 只有在真正需要显示网页内容时才会创建和初始化
        /// </summary>
        private SmartWebControl WbContent
        {
            get
            {
                if (wbContent == null && !_isImageMode)
                {
                    CreateWebControl();
                }
                return wbContent;
            }
        }

        /// <summary>
        /// 工具栏延迟加载属性
        /// 只有在真正需要显示工具栏时才会创建和初始化
        /// </summary>
        private ToolStrip ToolResize
        {
            get
            {
                if (toolResize == null)
                {
                    CreateToolResize();
                }
                return toolResize;
            }
        }

        internal UcContent(bool isImageMode = false)
        {
            _isImageMode = isImageMode;
            SetStyle(
                ControlStyles.UserPaint | ControlStyles.DoubleBuffer | ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.AllPaintingInWmPaint, true);
            InitializeComponent();

            IsImageMode = isImageMode;
            InitEmptyPanel();

            Load += UcContent_Load;
        }

        private async void UcContent_Load(object sender, EventArgs e)
        {
            InitializeControlProperties();
            try
            {
                EmptyPanel.ControlUseDrop();
            }
            catch { }

            BeginInvoke(new Action(() =>
            {
                InitBackColorDropDown();
                InitDisplayModeDropDown();
            }));
        }

        /// <summary>
        /// 创建工具栏的私有方法
        /// 只有在真正需要时才会被调用
        /// </summary>
        private void CreateToolResize()
        {
            if (toolResize != null) return;

            try
            {
                toolResize = new ToolStrip
                {
                    BackColor = Color.GhostWhite,
                    Dock = DockStyle.None,
                    GripStyle = ToolStripGripStyle.Hidden,
                    Location = new Point(56, 314),
                    Name = "toolResize",
                    Visible = false
                };

                toolResize.Items.AddRange(new ToolStripItem[] {
                    tsmImageViewBackStyle, tsmEdit, tsmCopy, tsmSearch, tsmTrans,
                    tsmPicBig, txtPicZoomPercent, tsmPicSmall, tsmPicOrigin, tsmPicType,
                    tsmRotate, tsbReOcr, tsbSaveImg, tsmModel, tsmNewWindow, tsmFullScreen
                });

                Controls.Add(toolResize);
                toolResize.Parent = this;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建工具栏失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建网页控件的私有方法
        /// 只有在真正需要时才会被调用
        /// </summary>
        private void CreateWebControl()
        {
            if (wbContent != null || _isImageMode)
                return;

            try
            {
                wbContent = new SmartWebControl
                {
                    Dock = DockStyle.Fill,
                    Location = new Point(0, 0),
                    Margin = new Padding(0),
                    MinimumSize = new Size(20, 20),
                    Name = "wbContent",
                    TabStop = false
                };

                // 绑定事件
                wbContent.LoadingStarted += WbContent_LoadingStarted;

                // 添加到控件集合
                Controls.Add(wbContent);
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常，保证程序继续运行
                Console.WriteLine($"创建网页控件失败: {ex.Message}");
            }
        }

        private void InitializeControlProperties()
        {
            // 数据表格初始化
            dgContent.IsShowSequence = true;

            // 文本控件初始化
            txtContent.LanguageOption = RichTextBoxLanguageOptions.UIFonts;
            txtContent.AllowDrop = true;
            txtContent.AutoWordSelection = false;
            txtContent.TextChanged += TxtContent_TextChanged;
            txtContent.LinkClicked += TxtContent_LinkClicked;

            // 图像控件初始化
            imageBox.Parent = this;
            imageBox.Dock = DockStyle.Fill;
            imageBox.ZoomChanged += ImageBoxZoomChanged;
            imageBox.SizeChanged += ImageBoxSizeChanged;

            ParentChanged += UcContent_ParentChanged;
            SizeChanged += UcContent_SizeChanged;
        }

        private void WbContent_LoadingStarted(object sender, string url)
        {
            if (!string.IsNullOrEmpty(url))
                if (url.StartsWith("file:"))
                {
                    var files = new List<string> { url.ToString().Replace("file:///", "") };
                    FrmMain.DragDropEventDelegate?.Invoke(files, null, null, ProcessBy.主界面, null);
                }
        }

        private void UcContent_SizeChanged(object sender, EventArgs e)
        {
            if (imageBox.SizeMode == ImageBoxSizeMode.Fit &&
                Equals(tsmPicOrigin.ToolTipText, "原始尺寸".CurrentText()))
            {
                imageBox.ZoomToFit();
            }
            ImageBoxSizeChanged(null, null);
        }

        private const string TraceInfo = "查看链路信息";

        private void TxtContent_LinkClicked(object sender, LinkClickedEventArgs e)
        {
            if (e.LinkText?.StartsWith(TraceInfo) == true)
            {
                ShowTraceInfo();
            }
        }

        public void ShowTraceInfo()
        {
            var lstTrace = OcrHelper.GetOcrTrace(OcrContent?.id);
            var strMsg = string.Join("\n", lstTrace);
            if (string.IsNullOrEmpty(strMsg))
            {
                strMsg = "未查询到调用链路信息！";
            }
            MessageBox.Show(this, strMsg, TraceInfo, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void UcContent_ParentChanged(object sender, EventArgs e)
        {
            CommonMethod.DetermineCall(this, delegate
            {
                var parent = FindForm() ?? Application.OpenForms[0];
                if (toolResize != null)
                    HighDpiHelper.ScaleToolStrip(1, CommonTheme.DpiScale, toolResize);
            });
        }

        private void TxtContent_TextChanged(object sender, EventArgs e)
        {
            CommonMethod.DetermineCall(this, () =>
            {
                ShowEmptyPanle(string.IsNullOrEmpty(txtContent.Text));
            });
        }

        const int WM_PARENTNOTIFY = 0x0210;
        protected override void WndProc(ref Message m)
        {
            if (m.Msg == WM_PARENTNOTIFY)
            {
                var frm = FindForm();
                if (frm != null)
                {
                    if (!frm.Focused)
                        frm.Activate();
                }
            }
            base.WndProc(ref m);
        }

        internal OcrContent OcrContent { get; set; }

        public Image Image { get { return imageBox.Image; } }

        internal SpiltMode SpiltModel
        {
            get => _currentSpiltModel;
            set
            {
                if (_currentSpiltModel != value)
                {
                    _currentSpiltModel = value;
                    BindContentByOcr(OcrContent);
                }
            }
        }

        public bool IsShowOldContent
        {
            get => _isShowOldContent;
            set
            {
                if (_isShowOldContent != value)
                {
                    _isShowOldContent = value;
                    BindContentByOcr(OcrContent, false, IsShowTxt);
                }
            }
        }

        internal KeyEventHandler TxtKeyDownEventDelegate
        {
            set
            {
                txtContent.KeyDown -= value;
                imageBox.KeyDown -= value;

                txtContent.KeyDown += value;
                imageBox.KeyDown += value;
            }
        }

        internal ContextMenuStrip MenuStrip
        {
            get => _menuStrip;
            set
            {
                _menuStrip = value;
                dgContent.ContextMenuStrip = _menuStrip;
                txtContent.ContextMenuStrip = _menuStrip;
                imageBox.ContextMenuStrip = _menuStrip;
            }
        }

        public void InitImageType()
        {
            if (OcrHelper.LstLocalImageType?.Count > 0)
            {
                图像增强ToolStripMenuItem.Visible = OcrHelper.LstLocalImageType.Exists(p => p.Code.Equals(ImageProcessType.图像增强.GetHashCode()));
                去屏幕纹ToolStripMenuItem.Visible = OcrHelper.LstLocalImageType.Exists(p => p.Code.Equals(ImageProcessType.去屏幕纹.GetHashCode()));
                老照片修复ToolStripMenuItem.Visible = OcrHelper.LstLocalImageType.Exists(p => p.Code.Equals(ImageProcessType.老照片修复.GetHashCode()));
            }
        }

        public int Zoom => imageBox.Zoom;

        public Color ImageBoxBackColor { get { return imageBox.BackColor; } }

        public void SetImageZoomBig(bool isLimitMax = false)
        {
            var fitZoom = imageBox.GetFitZoom(isLimitMax);
            if (fitZoom > ImageBox.MinZoom)
            {
                // 保存原始插值模式
                var originalInterpolationMode = imageBox.InterpolationMode;
                // 在动画过程中使用低质量模式
                imageBox.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.NearestNeighbor;

                var initZoom = Math.Max((int)(fitZoom * 0.05d), ImageBox.MinZoom);

                // 根据图片大小和缩放幅度动态调整步长数量
                int stepCount = 15;

                if (imageBox.Image != null)
                {
                    long imageSize = imageBox.Image.Width * imageBox.Image.Height;
                    if (imageSize > 4000000) // 大于400万像素
                        stepCount = 5;
                    else if (imageSize > 1000000) // 大于100万像素
                        stepCount = 10;

                    // 根据缩放幅度进一步调整
                    int zoomDifference = Math.Abs(fitZoom - imageBox.Zoom);
                    if (zoomDifference < 50) // 缩放变化较小
                        stepCount = Math.Max(2, stepCount / 2); // 至少2步，最多减半
                }

                // 使用优化后的缓动函数生成更有效的缩放序列
                var lstZoom = SplitNumbersWithEasing(initZoom, fitZoom, stepCount);

                // 跳过动画条件：图像过大或设备性能不足
                bool skipAnimation = imageBox.Image != null &&
                                    (imageBox.Image.Width * imageBox.Image.Height > 8000000 || // 800万像素以上
                                     SystemInformation.TerminalServerSession); // 远程会话

                if (skipAnimation)
                {
                    // 直接设置最终缩放值
                    imageBox.Zoom = fitZoom;
                    Update();
                }
                else
                {
                    // 执行动画缩放
                    foreach (var item in lstZoom)
                    {
                        imageBox.Zoom = item;
                        Update();
                    }
                }

                // 恢复原始插值模式
                imageBox.InterpolationMode = originalInterpolationMode;
            }

            if (!Equals(fitZoom, imageBox.Zoom))
                imageBox.ZoomToFit(isLimitMax);
        }

        /// <summary>
        /// 使用缓动函数生成平滑的缩放步长序列
        /// </summary>
        /// <param name="startNumber">起始缩放值</param>
        /// <param name="endNumber">目标缩放值</param>
        /// <param name="listLength">步长数量</param>
        /// <returns>缩放值序列</returns>
        private List<int> SplitNumbersWithEasing(int startNumber, int endNumber, int listLength)
        {
            List<int> result = new List<int>();

            // 优化：如果步数太少，直接线性分割
            if (listLength <= 3)
            {
                for (int i = 0; i < listLength; i++)
                {
                    double progress = (double)i / (listLength - 1);
                    int currentZoom = (int)(startNumber + (endNumber - startNumber) * progress);
                    result.Add(currentZoom);
                }
                return result;
            }

            // 优化：确保缩放值序列中没有重复值
            HashSet<int> uniqueValues = new HashSet<int>();
            uniqueValues.Add(startNumber);
            uniqueValues.Add(endNumber);

            for (int i = 1; i < listLength - 1; i++)
            {
                // 计算当前进度 (0.0 到 1.0)
                double progress = (double)i / (listLength - 1);

                // 应用改进的缓动函数，更加自然
                double easedProgress = ImprovedEasing(progress);

                // 计算当前缩放值
                int currentZoom = (int)(startNumber + (endNumber - startNumber) * easedProgress);

                // 只添加唯一值
                uniqueValues.Add(currentZoom);
            }

            // 转换为列表并排序
            result = uniqueValues.ToList();
            result.Sort();

            // 如果缩放是减小的，需要倒序
            if (startNumber > endNumber)
                result.Reverse();

            return result;
        }

        /// <summary>
        /// 改进的缓动函数，起止更平缓，中间过渡更快
        /// </summary>
        /// <param name="t">进度 (0.0 到 1.0)</param>
        /// <returns>缓动后的进度值</returns>
        private double ImprovedEasing(double t)
        {
            // 更加平滑的缓动函数，减少了中间状态数量
            return t < 0.5 ? 2 * t * t : 1 - Math.Pow(-2 * t + 2, 2) / 2;
        }

        private double EaseInOutCubic(double t)
        {
            return t < 0.5 ? 4 * t * t * t : 1 - Math.Pow(-2 * t + 2, 3) / 2;
        }

        public void SetImageZoomSmall()
        {
            var currentZoom = imageBox.Zoom;
            var targetZoom = ImageBox.MinZoom;

            // 如果当前缩放已经是最小值，不需要处理
            if (currentZoom <= targetZoom) return;

            // 保存原始插值模式
            var originalInterpolationMode = imageBox.InterpolationMode;
            // 在动画过程中使用低质量模式
            imageBox.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.NearestNeighbor;

            // 根据图片大小动态调整步长数量
            int stepCount = 15;
            if (imageBox.Image != null)
            {
                long imageSize = imageBox.Image.Width * imageBox.Image.Height;
                if (imageSize > 4000000) // 大于400万像素
                    stepCount = 5;
                else if (imageSize > 1000000) // 大于100万像素
                    stepCount = 10;

                // 根据缩放差距调整步数
                int zoomDifference = currentZoom - targetZoom;
                if (zoomDifference < 50)
                    stepCount = Math.Max(2, stepCount / 2);
            }

            // 跳过动画条件
            bool skipAnimation = imageBox.Image != null &&
                                (imageBox.Image.Width * imageBox.Image.Height > 8000000 ||
                                 SystemInformation.TerminalServerSession);

            if (skipAnimation)
            {
                // 直接设置最终缩放值
                imageBox.Zoom = targetZoom;
                Update();
            }
            else
            {
                // 生成平滑的缩放序列
                var lstZoom = SplitNumbersWithEasing(currentZoom, targetZoom, stepCount);

                foreach (var zoom in lstZoom)
                {
                    imageBox.Zoom = zoom;
                    Update();
                }
            }

            // 确保最终缩放值正确
            if (imageBox.Zoom != targetZoom)
                imageBox.Zoom = targetZoom;

            // 恢复原始插值模式
            imageBox.InterpolationMode = originalInterpolationMode;
        }

        private void ImageBoxSizeChanged(object sender, EventArgs e)
        {
            if (toolResize == null) return;

            // 计算工具栏水平居中位置
            int horizontalCenter = (int)((ClientRectangle.Width - toolResize.Width) * 1.0 / 2);

            // 根据显示模式调整工具栏垂直位置
            int verticalPosition;

            if (NowDisplayMode == DisplayModel.文字模式)
            {
                // 文字模式下，工具栏固定在底部，不考虑imageBox滚动条
                verticalPosition = ClientRectangle.Height - toolResize.Height - 5;
            }
            else
            {
                // 需要考虑imageBox的滚动条
                verticalPosition = ClientRectangle.Height - toolResize.Height
                    - (imageBox.Visible && imageBox.HScrollVisibile
                        ? SystemInformation.HorizontalScrollBarHeight
                        : 0) - 5;
            }

            // 设置工具栏位置
            toolResize.Location = new Point(horizontalCenter, verticalPosition);

            // 确保工具栏位于最前端
            if (toolResize.Visible)
            {
                toolResize.BringToFront();
            }

            SetFullScreenMenu();
        }

        private void SetFullScreenMenu()
        {
            if (toolResize == null || !toolResize.Visible)
                return;
            var isMax = FindForm()?.WindowState == FormWindowState.Maximized;
            toolResize.ShowItemToolTips = !isMax;
            if (tsmFullScreen.Visible)
            {
                if (isMax)
                {
                    tsmFullScreen.Image = ProcessStyleImage(Resources.退出全屏);
                }
                else
                {
                    tsmFullScreen.Image = ProcessStyleImage(Resources.全屏);
                }
            }
        }

        private void ImageBoxZoomChanged(object sender, EventArgs e)
        {
            if (imageBox.Zoom == 100)
            {
                tsmPicOrigin.Image = ProcessStyleImage(Resources.缩放);
                tsmPicOrigin.ToolTipText = "最佳缩放".CurrentText();
            }
            else
            {
                tsmPicOrigin.Image = ProcessStyleImage(Resources.原始);
                tsmPicOrigin.ToolTipText = "原始尺寸".CurrentText();
            }

            txtPicZoomPercent.Text = string.Format("{0}%", imageBox.Zoom);

            SetFullScreenMenu();
        }

        private Image ProcessStyleImage(Image image)
        {
            if (image == null) return null;
            image = ImageProcessHelper.ScaleImage(image, CommonTheme.DpiScale);
            return image;
        }

        Panel EmptyPanel;

        private void InitEmptyPanel()
        {
            EmptyPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackgroundImage = Resources.拖拽文件,
                BackgroundImageLayout = ImageLayout.Center,
                BackColor = Color.Transparent,
                Visible = true,
                AllowDrop = true,
                Parent = txtContent,
            };
            txtContent.Controls.Add(EmptyPanel);
            EmptyPanel.BringToFront();
        }

        internal void SetDragDrop()
        {
            this.ControlUseDrop();
            imageBox.ControlUseDrop();
            txtContent.ControlUseDrop();
            dgContent.ControlUseDrop();
        }

        internal void ShowEmptyPanle(bool isShow)
        {
            EmptyPanel.Visible = isShow;
            if (isShow)
            {
                txtContent.BringToFront();
                EmptyPanel.BringToFront();
                if (toolResize != null)
                    toolResize.Visible = false;
            }
        }

        public void SetCanClose()
        {
            var picClose = new PictureBox
            {
                BackColor = Color.Transparent,
                Size = new Size(32, 32), // 只覆盖必要区域
                Anchor = AnchorStyles.Top | AnchorStyles.Right,
                Cursor = Cursors.Hand,
                Visible = false,
                Image = ProcessStyleImage(Resources.Full_close_down),
                SizeMode = PictureBoxSizeMode.StretchImage,
                //Dock = DockStyle.Fill
            };

            this.Controls.Add(picClose);
            picClose.BringToFront();

            // 事件处理
            picClose.MouseDown += (sender, e) => FindForm()?.Close();
            picClose.MouseEnter += (sender, e) => picClose.Image = ProcessStyleImage(Resources.Full_close_hover);
            picClose.MouseLeave += (sender, e) => picClose.Image = ProcessStyleImage(Resources.Full_close_down);

            // 监听窗口状态和大小变化
            var form = FindForm();
            if (form != null)
            {
                form.SizeChanged += (s, e) => SetPicLocation(picClose);
            }
            this.SizeChanged += (s, e) => SetPicLocation(picClose);

            SetPicLocation(picClose);
        }

        private void SetPicLocation(PictureBox picClose)
        {
            var isMaximized = FindForm()?.WindowState == FormWindowState.Maximized;

            picClose.Visible = isMaximized;

            if (isMaximized)
            {
                picClose.Location = new Point(Width - picClose.Width - 20, 20);
            }
        }

        public void RefreshStyle()
        {
            this.SuspendLayout();
            try
            {
                // 批量设置文本框样式，避免SelectAll操作导致的闪烁
                var backgroundColor = CommonSetting.Get默认背景颜色();
                var foreColor = CommonSetting.Get默认文字颜色();
                var font = CommonSetting.Get默认文本字体();

                // 暂停文本框重绘
                txtContent.SuspendLayout();
                try
                {
                    txtContent.SelectAll();
                    txtContent.SelectionBackColor = backgroundColor;
                    txtContent.SelectionColor = foreColor;
                    txtContent.Select(0, 0);

                    txtContent.BackColor = backgroundColor;
                    txtContent.Font = font;
                    txtContent.ForeColor = foreColor;
                }
                finally
                {
                    txtContent.ResumeLayout(false);
                }

                // 批量设置数据表格样式
                dgContent.SuspendLayout();
                try
                {
                    dgContent.BackgroundColor = backgroundColor;
                    dgContent.Font = font;
                    dgContent.ForeColor = foreColor;
                }
                finally
                {
                    dgContent.ResumeLayout(false);
                }

                // 设置图像框样式
                imageBox.GridColor = CommonSetting.图片预览背景颜色;
                imageBox.GridDisplayMode = CommonSetting.ConvertToEnum(CommonSetting.图片预览背景, ImageBoxGridDisplayMode.马赛克);
                imageBox.InitBackGround();
            }
            finally
            {
                this.ResumeLayout(true);
            }
        }

        internal void BindContentByStr(string strContent, bool isAppend = false)
        {
            if (isAppend)
                txtContent.AppendText(Environment.NewLine + strContent);
            else
                txtContent.Text = strContent;
            if (!string.IsNullOrEmpty(txtContent.Text))
                txtContent.BringToFront();
            Application.DoEvents();
        }

        public bool IsShowToolBox { get; set; } = true;

        private DisplayModel _nowDisplayMode;

        internal DisplayModel NowDisplayMode
        {
            get => _nowDisplayMode;
            set
            {
                _nowDisplayMode = value;
                SetDisplayMode(_nowDisplayMode);
            }
        }

        /// <summary>
        /// 绑定OCR内容到控件
        /// </summary>
        /// <param name="ocr">OCR内容</param>
        /// <param name="isAppend">是否追加内容</param>
        /// <param name="isShowTxt">是否显示文本</param>
        internal void BindContentByOcr(OcrContent ocr, bool isAppend = false, bool? isShowTxt = false)
        {
            if (ocr?.result == null) return;

            SuspendLayout();

            try
            {
                if (isShowTxt.HasValue) IsShowTxt = isShowTxt.Value;
                OcrContent = ocr;

                switch (ocr.result.resultType)
                {
                    case ResutypeEnum.表格:
                        BindTableContent();
                        break;
                    case ResutypeEnum.网页:
                        BindWebContent();
                        break;
                    default:
                        if (OcrContent.ocrType == OcrType.公式)
                        {
                            BindFormulaContent();
                        }
                        else
                        {
                            BindTextContent(isAppend);
                        }
                        break;
                }
            }
            finally
            {
                ResumeLayout();
            }
        }

        /// <summary>
        /// 绑定表格内容
        /// </summary>
        private void BindTableContent()
        {
            try
            {
                var dt = CommonResult.GetTableContent(OcrContent.result);
                CommonMethod.DetermineCall(this, delegate
                {
                    try
                    {
                        dgContent.DataSource = dt;
                        dgContent.AutoResizeColumns();
                        dgContent.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.DisplayedCellsExceptHeaders;
                        foreach (DataGridViewColumn column in dgContent.Columns)
                        {
                            column.HeaderText = "";
                            column.MinimumWidth = 80;
                        }

                        dgContent.BringToFront();
                    }
                    catch { }
                });
            }
            catch { }
        }

        /// <summary>
        /// 绑定网页内容
        /// </summary>
        private void BindWebContent()
        {
            // 确保网页控件已创建（延迟加载）
            if (WbContent == null)
                return;

            CommonMethod.DetermineCall(this, delegate
            {
                WbContent.BringToFront();
            });
            var url = OcrHelper.GetFileResultUrl(OcrContent);
            WbContent.LoadAsync(url);
        }

        /// <summary>
        /// 绑定公式内容
        /// </summary>
        private void BindFormulaContent()
        {
            // 确保网页控件已创建（延迟加载）
            if (WbContent == null)
                return;

            CommonMethod.DetermineCall(this, delegate
            {
                WbContent.BringToFront();
            });
            var strPost = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(OcrContent.result?.autoText));
            WbContent.LoadAsync(CommonString.HostCode?.FullUrl + "math/view.aspx", strPost);
        }

        /// <summary>
        /// 绑定文本内容
        /// </summary>
        /// <param name="isAppend">是否追加内容</param>
        private void BindTextContent(bool isAppend)
        {
            CommonMethod.DetermineCall(this, delegate
            {
                try
                {
                    var contentStr = GetTextByContent(isAppend)?.TrimEnd() + Environment.NewLine;

                    if (isAppend)
                    {
                        AppendTextContent(contentStr);
                    }
                    else
                    {
                        SetNewTextContent(contentStr);
                    }

                    AppendLinkLabel();
                }
                catch { }
            });
        }

        /// <summary>
        /// 追加文本内容
        /// </summary>
        /// <param name="contentStr">要追加的内容</param>
        private void AppendTextContent(string contentStr)
        {
            if (string.IsNullOrEmpty(txtContent.Text))
            {
                contentStr = contentStr.TrimStart('\r').TrimStart('\n');
            }
            txtContent.Text += contentStr;
        }

        /// <summary>
        /// 设置新的文本内容
        /// </summary>
        /// <param name="contentStr">新的内容</param>
        private void SetNewTextContent(string contentStr)
        {
            txtContent.Text = contentStr;

            var isVertical = IsCanVertical();
            tsmModel.Visible = !(FindForm() is FrmMain) && isVertical;

            if (isVertical)
            {
                NowDisplayMode = NowDisplayMode;
            }
            else
            {
                SetDisplayMode(DisplayModel.文字模式);
            }
        }

        private void AppendLinkLabel()
        {
            if (!txtContent.Text.StartsWith("识别失败"))
            {
                return;
            }
            txtContent.InsertLink(TraceInfo, txtContent.TextLength);
        }

        public void ShowImageTool()
        {
            ToolResize.Visible = IsShowToolBox && OcrContent != null && OcrContent.result != null
                                 && Equals(OcrContent.result.resultType, ResutypeEnum.文本) && !Equals(OcrContent.ocrType, OcrType.公式);
            if (ToolResize.Visible)
            {
                var isVertical = IsCanVertical();
                tsmModel.Visible = !(FindForm() is FrmMain) && isVertical && !IsImageMode;
                ImageBoxSizeChanged(null, null);
            }
        }

        public void BindImage(Image image, bool isBindImageOnly, bool isAutoSize)
        {
            try
            {
                imageBox.SetImage(image, isAutoSize);

                // 重置图像类型为原始图像
                if (!Equals(tsmPicType.ToolTipText, 原始图像ToolStripMenuItem.Text))
                    tsmPicType_DropDownItemClicked(this, new ToolStripItemClickedEventArgs(原始图像ToolStripMenuItem));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ucContent.BindImage error: {ex.Message}");
            }
        }

        private string GetTextByContent(bool isAppend = false)
        {
            var result = OcrContent.result.GetTextResult(CommonSetting.首行缩进, isAppend, IsShowOldContent, SpiltModel
                , Equals(OcrContent.ocrType, OcrType.翻译), OcrContent.processName);
            return result;
        }

        internal string GetTextByContent(TextCellInfo cell)
        {
            var result = cell.GetCellContent(OcrContent?.ocrType == OcrType.翻译, IsShowOldContent);
            return result;
        }

        public string GetContentText(bool isAll = true)
        {
            // 在文档模式下，优先获取imageBox中选中的文本，如果没有选中则返回全部文本
            if (NowDisplayMode == DisplayModel.文档模式)
            {
                var selectedText = imageBox?.GetSelectedTextContent();
                return string.IsNullOrEmpty(selectedText) ? txtContent.Text : selectedText;
            }

            // 其他模式保持原有逻辑
            return isAll
                ? string.IsNullOrEmpty(txtContent.SelectedText) ? txtContent.Text : txtContent.SelectedText
                : txtContent.SelectedText;
        }

        public void ExportExcel()
        {
            if (dgContent.DataSource == null)
            {
                MessageBox.Show(this, "没有数据！".CurrentText(), CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var text = ServerTime.DateTime.ToDateStr("yyyyMMddhhmmss");
            var saveFileDialog1 = new SaveFileDialog
            {
                Filter = "Excel|*.xls;*.csv;*.xlsx;",
                Title = "选择保存位置".CurrentText(),
                FileName = "Excel_" + text,
                FilterIndex = 1
            };
            if (saveFileDialog1.ShowDialog(this) != DialogResult.OK ||
                string.IsNullOrEmpty(saveFileDialog1.FileName)) return;
            ExcelHelper.ExportCsv(dgContent, saveFileDialog1.FileName);
        }

        #region 图片模式渲染

        public bool IsCanVertical()
        {
            return OcrContent?.IsCanVertical == true;
        }

        public bool IsShowTxt { get; set; }

        public async void BindPicTxt()
        {
            if (FindForm() is FrmMain)
            {
                tsmNewWindow.Visible = true;
                tsmFullScreen.Visible = false;
            }
            else
            {
                tsmNewWindow.Visible = false;
                tsmFullScreen.Visible = true;
            }

            imageBox.BindPicTxt(this, IsShowTxt);
        }

        private void tsmPicBig_Click(object sender, EventArgs e)
        {
            imageBox.ZoomIn(true);
        }

        private void tsmPicOrigin_Click(object sender, EventArgs e)
        {
            if (imageBox.Zoom == 100)
                imageBox.ZoomToFit();
            else
                imageBox.Zoom = 100;
        }

        private void tsmPicSmall_Click(object sender, EventArgs e)
        {
            imageBox.ZoomOut(true);
        }

        /// <summary>
        /// 初始化显示模式下拉菜单
        /// </summary>
        private void InitDisplayModeDropDown()
        {
            try
            {
                // 清除现有下拉菜单项
                tsmModel.DropDownItems.Clear();

                // 使用反射获取DisplayModel枚举的所有值
                var displayModes = Enum.GetValues(typeof(DisplayModel));

                // 为每个模式创建菜单项
                foreach (DisplayModel mode in displayModes)
                {
                    var menuItem = new ToolStripMenuItem
                    {
                        Text = mode.ToString().CurrentText(),
                        Tag = mode,
                        Image = ProcessStyleImage(tsmModel.SetResourceImage(mode.ToString()))
                    };

                    // 添加点击事件
                    menuItem.Click += DisplayModeItem_Click;

                    // 添加到下拉菜单
                    tsmModel.DropDownItems.Add(menuItem);
                }

                // 更新当前选中状态
                UpdateModeButtonText(_nowDisplayMode);
            }
            catch (Exception ex)
            {
                Log.WriteError("InitDisplayModeDropDown", ex);
            }
        }

        /// <summary>
        /// 显示模式下拉菜单项点击事件
        /// </summary>
        private void DisplayModeItem_Click(object sender, EventArgs e)
        {
            if (sender is ToolStripMenuItem item && item.Tag is DisplayModel mode)
            {
                // 设置显示模式
                NowDisplayMode = mode;

                tsmModel.Image = ProcessStyleImage(tsmModel.SetResourceImage(mode.ToString()));
                tsmModel.Text = string.Format("【{0}】", mode.ToString().CurrentText());

                // 更新菜单项选中状态
                foreach (ToolStripMenuItem menuItem in tsmModel.DropDownItems)
                {
                    menuItem.Checked = (menuItem.Tag is DisplayModel itemMode) && (itemMode == mode);
                }
            }
        }

        /// <summary>
        /// 旧的切换模式点击事件，保留作为兼容性支持
        /// </summary>
        private void tsmModel_Click(object sender, EventArgs e)
        {
            // 新的下拉菜单已经处理了模式切换
            // 这里只需要确保鼠标位置正确
            if (toolResize != null)
            {
                var mouseLocation = toolResize.PointToScreen(tsmModel.Bounds.Location);
                CommonMethod.SetCursorPos(mouseLocation.X + tsmModel.Width / 2, mouseLocation.Y + tsmModel.Height / 2);
            }
        }

        private void SetDisplayMode(DisplayModel model)
        {
            if (model == DisplayModel.文字模式)
            {
                // 文字模式：仅显示文本内容
                tsmImageViewBackStyle.Visible = false;
                txtPicZoomPercent.Visible = false;
                tsmTrans.Visible = Program.NowUser?.IsSupportTranslate == true;
                tsmSearch.Visible = true;
                tsmPicBig.Visible = false;
                tsmPicOrigin.Visible = false;
                tsmPicSmall.Visible = false;

                // 禁用文档模式
                imageBox.EnableDocumentMode(false);

                // 隐藏图片，显示文本
                imageBox.Visible = false;
                txtContent.Visible = true;
                txtContent.BringToFront();
            }
            else if (model == DisplayModel.图文模式)
            {
                var isEmpty = string.IsNullOrEmpty(OcrContent?.result?.autoText);

                // 工具栏按钮设置
                tsmSearch.Visible = false;
                tsmTrans.Visible = false;
                txtPicZoomPercent.Visible = !isEmpty || IsImageMode;
                tsmPicBig.Visible = !isEmpty || IsImageMode;
                tsmPicOrigin.Visible = !isEmpty || IsImageMode;
                tsmPicSmall.Visible = !isEmpty || IsImageMode;
                tsmCopy.Visible = !isEmpty;
                tsmModel.Visible = !(FindForm() is FrmMain) && !isEmpty;
                tsbReOcr.Visible = IsImageMode;
                tsbSaveImg.Visible = IsImageMode;
                tsmRotate.Visible = IsImageMode;
                tsmImageViewBackStyle.Visible = IsImageMode;
                tsmEdit.Visible = isEmpty || FindForm() is FormViewImage;

                // 配置控件属性
                imageBox.Dock = DockStyle.Fill;
                txtContent.Dock = DockStyle.Fill;

                // 禁用文档模式
                imageBox.EnableDocumentMode(false);

                // 图文模式：如果有图片就显示图片，否则显示文本
                var hasImage = imageBox.Image != null;
                if (hasImage || IsImageMode)
                {
                    txtContent.Visible = false;
                    imageBox.Visible = true;
                    imageBox.BringToFront();

                    // 根据是否有图片设置缩放策略
                    imageBox.ScalingStrategy = hasImage ? ImageBox.ImageScalingStrategy.FitInteractive : ImageBox.ImageScalingStrategy.FitComplete;
                    imageBox.ZoomToFit();
                }
                else
                {
                    imageBox.Visible = false;
                    txtContent.Visible = true;
                    txtContent.BringToFront();
                }
            }
            else if (model == DisplayModel.文档模式)
            {
                // 文档模式：图片+精确文字选择功能
                var isEmpty = string.IsNullOrEmpty(OcrContent?.result?.autoText);

                // 工具栏按钮设置（类似图文模式，但增加文档特有功能）
                tsmSearch.Visible = false;
                tsmTrans.Visible = false;
                txtPicZoomPercent.Visible = !isEmpty;
                tsmPicBig.Visible = !isEmpty;
                tsmPicOrigin.Visible = !isEmpty;
                tsmPicSmall.Visible = !isEmpty;
                tsmCopy.Visible = !isEmpty;
                tsmModel.Visible = !(FindForm() is FrmMain) && !isEmpty;
                tsbReOcr.Visible = isEmpty;
                tsbSaveImg.Visible = isEmpty;
                tsmRotate.Visible = isEmpty;
                tsmImageViewBackStyle.Visible = IsImageMode;
                tsmEdit.Visible = isEmpty || FindForm() is FormViewImage;

                // 配置控件属性
                imageBox.Dock = DockStyle.Fill;
                txtContent.Dock = DockStyle.Fill;

                // 文档模式：始终显示图片，启用文档选择功能
                txtContent.Visible = false;
                imageBox.Visible = true;
                imageBox.BringToFront();

                // 启用文档模式的特殊功能
                imageBox.EnableDocumentMode(true);

                if (imageBox.OriginImage != null && imageBox.Image != imageBox.OriginImage)
                {
                    imageBox.SetImage(imageBox.OriginImage, CommonSetting.图片自动缩放);
                }

                // 从图文分离模式切换过来时，重新调整图片尺寸
                if (imageBox.Image != null)
                {
                    // 文档模式使用清晰显示策略
                    imageBox.ScalingStrategy = ImageBox.ImageScalingStrategy.FitInteractive;
                    imageBox.ZoomToFit();
                }
                else
                {
                    imageBox.Visible = false;
                    txtContent.Visible = true;
                    txtContent.BringToFront();
                }
            }

            // 更新模式切换按钮文本和图标
            UpdateModeButtonText(model);

            imageBox.IsShowTip = _nowDisplayMode == DisplayModel.图文模式;
            if (_nowDisplayMode != DisplayModel.文字模式 && OcrContent?.IsCanVertical == true)
                BindPicTxt();

            // 强制更新工具栏状态和位置
            ImageBoxZoomChanged(null, null);
            ImageBoxSizeChanged(null, null);
        }

        /// <summary>
        /// 更新显示模式相关按钮状态
        /// </summary>
        private void UpdateModeButtonText(DisplayModel currentModel)
        {
            // 更新下拉菜单项的选中状态
            foreach (ToolStripMenuItem menuItem in tsmModel.DropDownItems)
            {
                if (menuItem.Tag is DisplayModel itemMode)
                {
                    menuItem.Checked = (itemMode == currentModel);
                }
            }

            // 将tsmModel设置为下拉按钮
            tsmModel.Text = currentModel.ToString().CurrentText();
            tsmModel.Image = ProcessStyleImage(tsmModel.SetResourceImage(currentModel.ToString()));
        }

        #endregion

        #region 其他按钮功能
        public bool IsCanMax { get { return imageBox?.IsCanMax ?? true; } set { if (imageBox != null) { imageBox.IsCanMax = value; } } }

        private void tsmFullScreen_Click(object sender, EventArgs e)
        {
            imageBox.SwitchMaxWindow();
        }

        private void tsmSearch_Click(object sender, EventArgs e)
        {
            var txt = GetContentText();
            if (!string.IsNullOrEmpty(txt)) FrmMain.DoSearch(txt);
        }

        private void tsmPicView_Click(object sender, EventArgs e)
        {
            if (Image == null)
            {
                return;
            }

            if (string.IsNullOrEmpty(OcrContent.processName))
            {
                this.ViewImage(Image);
            }
            else
            {
                if (FindForm() is MetroForm frm)
                {
                    var compare = new FrmPicCompare { Icon = frm.Icon };
                    compare.Init(SpiltModel, IsShowOldContent);
                    compare.Bind(Image, OcrContent, NowDisplayMode);
                    compare.Show();
                }
            }
        }

        private void tsmCopy_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(txtContent.Text))
                ClipboardService.SetText(txtContent.Text);
        }

        private void tsmTrans_Click(object sender, EventArgs e)
        {
            var transText = string.IsNullOrEmpty(txtContent.SelectedText) ? txtContent.Text : txtContent.SelectedText;
            if (string.IsNullOrEmpty(transText)) return;
            var files = new List<string> { "data:txt" + transText };
            FrmMain.DragDropEventDelegate?.Invoke(files, null, OcrType.翻译, ProcessBy.主界面, null);
        }

        #endregion

        private void tsmRotate_Click(object sender, EventArgs e)
        {
            if (imageBox.Image == null)
            {
                return;
            }
            imageBox.Image.RotateFlip(RotateFlipType.Rotate90FlipNone);
            imageBox.Invalidate();
            //imageBox.ZoomToFit();
            //if (imageBox.Zoom > 100) imageBox.Zoom = 100;
        }

        private void tsbSaveImg_Click(object sender, EventArgs e)
        {
            if (imageBox.Image != null)
                new Bitmap(imageBox.Image).SaveFile(this);
        }

        private void tsbReOcr_Click(object sender, EventArgs e)
        {
            if (imageBox.Image != null)
                imageBox.Image.Ocr();
        }

        private void InitBackColorDropDown()
        {
            ToolStripMenuItem checkItem = null;
            foreach (ImageBoxGridDisplayMode type in Enum.GetValues(typeof(ImageBoxGridDisplayMode)))
            {
                var item = new ToolStripMenuItem
                {
                    Tag = type.GetHashCode(),
                    AutoToolTip = false,
                    Checked = Equals(type.ToString(), CommonSetting.图片预览背景),
                    Text = type.ToString()
                };
                item.Image = item.SetResourceImage("背景_" + type);
                if (item.Checked)
                {
                    checkItem = item;
                }
                tsmImageViewBackStyle.DropDownItems.Add(item);
            }

            if (checkItem == null)
            {
                checkItem = tsmImageViewBackStyle.DropDownItems[0] as ToolStripMenuItem;
            }

            if (checkItem != null) tsmImageViewBackStyle.Image = checkItem.Image;
        }

        private void tsmImageViewBackStyle_DropDownItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            var item = e.ClickedItem;
            foreach (ToolStripMenuItem oldItem in tsmImageViewBackStyle.DropDownItems) oldItem.Checked = Equals(item, oldItem);
            if (item?.Tag == null) return;
            var imgType = (ImageBoxGridDisplayMode)BoxUtil.GetInt32FromObject(item.Tag?.ToString());
            tsmImageViewBackStyle.Image = item.Image;

            var backColor = CommonSetting.图片预览背景颜色;
            switch (imgType)
            {
                case ImageBoxGridDisplayMode.豆沙绿:
                case ImageBoxGridDisplayMode.深沉黑:
                case ImageBoxGridDisplayMode.办公灰:
                    backColor = ImageBoxGridDisplayModeHelper.GetBackColorByType(imgType);
                    break;
                case ImageBoxGridDisplayMode.自定义:
                    {
                        if (!ColorPickerForm.PickColor(backColor, out var newColor, true, FindForm()))
                            newColor = Color.FromArgb(238, 243, 250);
                        backColor = newColor;
                        break;
                    }
            }
            CommonSetting.SetValue("图片预览背景", imgType.ToString());
            CommonSetting.SetValue("图片预览背景颜色", backColor);

            imageBox.GridColor = backColor;
            imageBox.GridDisplayMode = imgType;
            imageBox.InitBackGround();
        }

        private void tsmPicType_DropDownItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            var item = e.ClickedItem;
            if (item == null || item.Tag == null) return;
            if (imageBox.OriginImage == null && imageBox.Image != null)
            {
                imageBox.OriginImage = new Bitmap(imageBox.Image);
            }
            if (imageBox.OriginImage == null) return;

            //tsmPicType.BackgroundImage = item.Image;
            tsmPicType.Image = item.Image; //tsmPicType.BackgroundImage;
            tsmPicType.ToolTipText = item.Text;
            var imgType = (ImageProcessType)BoxUtil.GetInt32FromObject(item.Tag?.ToString());
            int oldZoom = imageBox.Zoom;
            try
            {
                imageBox.Image = ImageProcessHelper.ProcessImage(new Bitmap(imageBox.OriginImage), imgType);
            }
            catch (Exception oe)
            {
                Log.WriteError("tsmPicType_DropDownItemClicked:" + imgType.ToString(), oe);
            }
            imageBox.Tag = null;
            imageBox.Zoom = oldZoom;
        }

        private void tsmEdit_Click(object sender, EventArgs e)
        {
            CommonMethod.EditImage(Image);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                UnregisterEventHandlers();

                try
                {
                    // 在释放imageBox前，先释放其中的图像资源
                    if (imageBox?.Image != null)
                    {
                        ImageResourceManager.ReleaseImage(imageBox.Image);
                    }

                    if (imageBox?.OriginImage != null && imageBox.OriginImage != imageBox.Image)
                    {
                        ImageResourceManager.ReleaseImage(imageBox.OriginImage);
                    }

                    // 清空引用，避免重复释放
                    if (imageBox != null)
                    {
                        imageBox.Image = null;
                        imageBox.OriginImage = null;
                    }

                    // 正常释放组件
                    imageBox?.Dispose();
                    txtContent?.Dispose();
                    wbContent?.Dispose();
                    toolResize?.Dispose();
                    EmptyPanel?.Dispose();
                    dgContent?.Dispose();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error disposing ucContent: {ex.Message}");
                }

                components.Dispose();
            }

            // 取消注册事件
            UnregisterEventHandlers();

            base.Dispose(disposing);
        }

        private void UnregisterEventHandlers()
        {
            Load -= UcContent_Load;
            ParentChanged -= UcContent_ParentChanged;
            SizeChanged -= UcContent_SizeChanged;

            txtContent.TextChanged -= TxtContent_TextChanged;
            txtContent.LinkClicked -= TxtContent_LinkClicked;

            if (wbContent != null)
                wbContent.LoadingStarted -= WbContent_LoadingStarted;

            imageBox.ZoomChanged -= ImageBoxZoomChanged;
            imageBox.SizeChanged -= ImageBoxSizeChanged;
        }
    }

    public enum DisplayModel
    {
        文字模式 = 0,
        图文模式 = 1,
        文档模式 = 2
    }
}